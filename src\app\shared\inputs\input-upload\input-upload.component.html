<nz-form-item [formGroup]="parentForm">
  <nz-form-control>
    <div class="clearfix">
      <nz-form-label [nzRequired]="isRequired()">
        <i
          nz-icon
          class="info-icon"
          nz-popover
          [nzPopoverTitle]="'limits.limit' | translate"
          [nzPopoverContent]="tplImageInfo"
          nzType="info-circle"
        ></i>
        <div>{{ label | translate }}</div>
        <ng-template #tplImageInfo>
          <div class="info-image">
            <strong>{{ "limits.width" | translate }}: </strong>
            <div class="min-max">
              <span>min {{ Limits.width.min }}px</span>
              <span>max {{ Limits.width.max }}px</span>
            </div>
            <strong>{{ "limits.heigth" | translate }}: </strong>
            <div class="min-max">
              <span>min {{ Limits.height.min }}px</span>
              <span>max {{ Limits.height.max }}px</span>
            </div>
          </div>
        </ng-template>
      </nz-form-label>
      <nz-upload
        class="nz-upload-custom-container"
        #upload
        [nzAction]="'http://localhost:4201/api/fakeImage'"
        [nzCustomRequest]="uploadRequest"
        [(nzFileList)]="FileList"
        [nzShowButton]="FileList.length < maxImages"
        [nzFileType]="
          'image/png,image/jpeg,image/gif,image/bmp,image/jpg,image/webp'
        "
        [nzHeaders]="setMediaUploadHeaders"
        [nzPreview]="handlePreview"
        [nzRemove]="removeItem"
        [nzShowUploadList]="showUploadList"
        nzListType="picture-card"
      >
        <div>
          <i nz-icon nzType="plus"></i>
          <div style="margin-top: 8px">Carica</div>
        </div>
      </nz-upload>
      <nz-modal
        [nzVisible]="previewVisible"
        [nzContent]="modalContent"
        [nzFooter]="null"
        (nzOnCancel)="previewVisible = false"
      >
        <ng-template #modalContent>
          <img
            [src]="previewImage"
            [ngStyle]="{ width: '100%' }"
            alt="Immagine"
          />
        </ng-template>
      </nz-modal>
    </div>
  </nz-form-control>
</nz-form-item>
