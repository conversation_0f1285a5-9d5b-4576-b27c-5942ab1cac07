import { Ng<PERSON>tyle } from '@angular/common';
import { Component, effect, input, Input, Renderer2 } from '@angular/core';
import {
  AbstractControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

import { ImageService } from '@core/services/utils/image';
import { UploadService } from '@core/services/utils/upload';
import { log } from '@core/utils/logger';
import {
  IFileUpload,
  IImageSize,
  ShowUploadListImage,
} from '@models/interfaces/file-upload';
import { TranslateModule } from '@ngx-translate/core';
import {
  NzFormControlComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzPopoverDirective } from 'ng-zorro-antd/popover';
import {
  NzUploadFile,
  NzUploadModule,
  NzUploadXHRArgs,
} from 'ng-zorro-antd/upload';
import { delay, of } from 'rxjs';

const getBase64 = (file: File): Promise<string | ArrayBuffer | null> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });

enum ValidatorsField {
  IMAGES = 'images',
}

const IMAGE_LIMITS: IImageSize = {
  width: {
    min: 200,
    max: 2000,
  },
  height: {
    min: 200,
    max: 2000,
  },
  size: {
    kilobytes: 16,
    megabytes: 16,
    gigabytes: 16,
  },
};

@Component({
  selector: 'app-input-upload',
  standalone: true,
  imports: [
    NzUploadModule,
    NzFormLabelComponent,
    NzFormControlComponent,
    FormsModule,
    ReactiveFormsModule,
    NzModalModule,
    NgStyle,
    NzPopoverDirective,
    TranslateModule,
  ],
  providers: [UploadService],
  templateUrl: './input-upload.component.html',
  styleUrl: './input-upload.component.less',
})
export class InputUploadComponent {
  @Input() parentForm: FormGroup | any;
  @Input() controlName: string;
  @Input() label: string;
  @Input() disabled: boolean = false;
  @Input() maxTagCount: number;
  @Input() fileType: string;
  @Input() showAddButton: boolean = true;
  @Input() listType: 'text' | 'picture' | 'picture-card' = 'picture-card';
  @Input() imageContainerSize?: {
    width: string;
    height: string;
  };
  @Input() maxImages: number = 1;
  @Input() customUploadRequest?: (item: NzUploadXHRArgs) => any;
  @Input() customRemoveHandler?: (file: NzUploadFile) => boolean;
  @Input() customUploadService?: UploadService;

  fileList = input<NzUploadFile[]>([]);
  imageList = input<IFileUpload[]>([]);

  protected fileListEffect = effect(() => {
    this.fileList();
    this.getActiveUploadService().FileList = this.fileList();
  });

  protected imageListEffect = effect(() => {
    this.imageList();
    this.getActiveUploadService().ImageList = this.imageList();
    console.log('Image list effect', this.imageList());
  });

  protected previewImage: string | undefined = '';
  protected previewVisible = false;

  protected emptyImageElement = '.ant-upload.ant-upload-select-picture-card';
  protected listImageElement = '.ant-upload-list-picture-card-container';

  constructor(
    private renderer2: Renderer2,
    private imageService: ImageService,
    private uploadService: UploadService,
  ) {}

  /**
   * Restituisce l'upload service attivo (personalizzato o di default)
   */
  private getActiveUploadService(): UploadService {
    return this.customUploadService || this.uploadService;
  }

  ngOnInit(): void {
    this.getActiveUploadService().FileList = this.fileList();
  }

  ngAfterViewInit(): void {
    const imageContainer = document.querySelector(this.emptyImageElement);
    log('Image container', imageContainer);
    this.#setContainerSize(imageContainer);
    const listContainer = document.querySelector(this.listImageElement);
    log('List conainer', listContainer);
    this.#setContainerSize(listContainer);
  }

  #setContainerSize(element: Element): void {
    if (
      this.imageContainerSize?.width &&
      this.imageContainerSize?.height &&
      element
    ) {
      this.renderer2.setStyle(element, 'width', this.imageContainerSize?.width);
      this.renderer2.setStyle(
        element,
        'height',
        this.imageContainerSize?.height,
      );
      this.renderer2.setStyle(element, 'margin-top', '-22px');
    }
  }

  handlePreview = async (file: NzUploadFile): Promise<void> => {
    if (!file.url && !file['preview']) {
      file['preview'] = await getBase64(file.originFileObj!);
    }
    this.previewImage = file.url || file['preview'];
    this.previewVisible = true;
  };

  isRequired() {
    return this.parentForm
      .get(this.controlName)
      .hasValidator(Validators.required);
  }

  public uploadRequest = (item: NzUploadXHRArgs) => {
    // Usa l'handler personalizzato se disponibile
    if (this.customUploadRequest) {
      return this.customUploadRequest(item);
    }

    // Altrimenti usa l'implementazione di default
    return this.imageService.uploadFileLocal(item, IMAGE_LIMITS).subscribe({
      next: (image) => {
        log('UPLAOD OK', image);
        of(image)
          .pipe(delay(200))
          .subscribe(() => {
            const activeService = this.getActiveUploadService();
            activeService.FileList = activeService.FileList.map((file) => {
              console.log('FILE', file);
              return {
                ...file,
                thumbUrl: file.response?.base64,
              };
            });
            this.imagesField.patchValue(activeService.FileList);
          });
      },
      error: (err) => {
        log('ERROR IMAGE', err);
      },
      complete: () => {
        log('FILE LIST', this.uploadService.FileList);
      },
    });
  };

  public setMediaUploadHeaders = () => {
    return {
      'Content-Type': 'multipart/form-data',
      Accept: 'application/json',
    };
  };

  public removeItem = (file: NzUploadFile): boolean => {
    // Usa l'handler personalizzato se disponibile
    if (this.customRemoveHandler) {
      return this.customRemoveHandler(file);
    }

    // Altrimenti usa l'implementazione di default
    const activeService = this.getActiveUploadService();
    activeService.removeFiles(file);
    this.imagesField.patchValue(activeService.FileList);
    activeService.FileList.length <= 0 ||
    !activeService.FileList.every((image) => image.error === undefined)
      ? !!this.parentForm.get(this.controlName).valid
      : !this.parentForm.get(this.controlName).valid;
    return false;
  };

  public get FileList(): NzUploadFile[] {
    return this.getActiveUploadService().FileList;
  }

  public set FileList(list: NzUploadFile[]) {
    this.getActiveUploadService().FileList = list;
  }

  public get imagesField(): AbstractControl {
    return this.parentForm.get(ValidatorsField.IMAGES)!;
  }

  public get Limits(): IImageSize {
    return IMAGE_LIMITS;
  }

  public showUploadList: ShowUploadListImage = {
    showPreviewIcon: true,
    showRemoveIcon: true,
    hidePreviewIconInNonImage: true,
  };
}
