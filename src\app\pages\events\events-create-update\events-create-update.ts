import {
  afterNextRender,
  Component,
  DestroyRef,
  inject,
  output,
  Signal,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { EventsService } from '@core/services/http/events';
import { BreadcrumbService } from '@core/services/utils/breadcrumb';
import { HeaderService } from '@core/services/utils/header';
import { ImageService } from '@core/services/utils/image';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { UploadService, ValidateImages } from '@core/services/utils/upload';
import { FormUtils } from '@core/utils/form';
import { getBase64Async } from '@core/utils/image';
import { log } from '@core/utils/logger';
import { CreateUpdateItem } from '@models/entities/create-update-item';
import { crudActionType } from '@models/enums/crud-action-type';
import { currentSectionType } from '@models/enums/current-section';
import { IEvent } from '@models/interfaces/event';
import {
  IImageSize,
  ShowUploadListImage,
} from '@models/interfaces/file-upload';
import { TranslateModule } from '@ngx-translate/core';
import { InputCheckboxComponent } from '@shared/inputs/input-checkbox/input-checkbox';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { InputSingleDatepickerComponent } from '@shared/inputs/input-single-datepicker/input-single-datepicker';
import { InputTextareaComponent } from '@shared/inputs/input-textarea/input-textarea';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import {
  NzFormControlComponent,
  NzFormDirective,
  NzFormItemComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzModalComponent } from 'ng-zorro-antd/modal';
import { NzPopoverDirective } from 'ng-zorro-antd/popover';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import {
  NzUploadComponent,
  NzUploadFile,
  NzUploadXHRArgs,
} from 'ng-zorro-antd/upload';
import { delay, of, switchMap, tap } from 'rxjs';

enum ValidatorsField {
  IMAGES = 'images',
}

const IMAGE_LIMITS: IImageSize = {
  width: {
    min: 200,
    max: 2000,
  },
  height: {
    min: 200,
    max: 2000,
  },
  size: {
    kilobytes: 16,
    megabytes: 16,
    gigabytes: 16,
  },
};

@Component({
  selector: 'app-events-create-update',
  imports: [
    ReactiveFormsModule,
    FormsModule,
    NzFormDirective,
    NzFormItemComponent,
    NzFormLabelComponent,
    NzFormControlComponent,
    NzSpinComponent,
    NzRowDirective,
    NzColDirective,
    NzUploadComponent,
    NzModalComponent,
    NzButtonComponent,
    NzIconDirective,
    NzPopoverDirective,
    TranslateModule,
    InputGenericComponent,
    InputTextareaComponent,
    InputSingleDatepickerComponent,
    InputCheckboxComponent,
  ],
  providers: [UploadService],
  templateUrl: './events-create-update.html',
  styleUrl: './events-create-update.less',
})
export class EventsCreateUpdate extends CreateUpdateItem {
  // SERVICES
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private uploadService = inject(UploadService);
  private imageService = inject(ImageService);
  private eventsService = inject(EventsService);
  private router = inject(Router);
  private messageService = inject(MessageService);
  private modalService = inject(ModalService);
  private headerService = inject(HeaderService);
  private fb = inject(FormBuilder);
  private breadcrumbService = inject(BreadcrumbService);

  protected baseForm!: FormGroup;
  // Convert to signals
  protected saveButtonTitle = signal<string>('EVENTS.saveEvent');
  protected abortButtonTitle = signal<string>('EVENTS.deleteEvent');
  public crudMode = signal<crudActionType>(crudActionType.create);
  protected crudActionType = crudActionType;
  protected isValidForm = signal<boolean>(false);
  protected eventId = signal<string>('');
  protected loading = signal<boolean>(true);
  protected event: Signal<IEvent | undefined> = this.eventsService.event$;

  // CONSTANT SIGNALS
  protected showEventStatus = signal([
    { label: 'EVENTS.showOnline', value: 'online', checked: true },
  ]);

  /**
   * Inizializza il servizio di upload con le immagini esistenti dell'evento.
   * Popola FileList e ImageList con i dati dal form.
   * @returns void
   */
  private initUploadService(): void {
    this.uploadService.FileList = FormUtils.populateImages(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
    this.uploadService.ImageList = FormUtils.populateField(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
  }

  protected previewImage: string | undefined = '';
  protected previewVisible = false;

  // VIEWCHILD
  public tplButton = viewChild<TemplateRef<any>>('tplButton');

  // OUTPUT
  componentReady = output<TemplateRef<any>>();

  /**
   * Costruttore del componente events create/update.
   * Inizializza il form, gestisce i parametri di routing e configura l'header.
   * @returns void
   */
  constructor() {
    super();
    this.initForm();

    this.route.paramMap
      .pipe(
        takeUntilDestroyed(),
        switchMap((params) => {
          this.eventId.set(<string>params.get('id'));
          return this.route.data.pipe(
            tap((data) => {
              this.crudMode.set(<crudActionType>data['crudType']);
              this.setCrudMode();
            }),
          );
        }),
      )
      .subscribe();

    afterNextRender(() => {
      this.headerService.setCurrentSection(
        currentSectionType.eventDetail,
        this.tplButton(),
      );
    });
  }

  public get FileList(): NzUploadFile[] {
    return this.uploadService.FileList;
  }

  public set FileList(list: NzUploadFile[]) {
    this.uploadService.FileList = list;
  }

  public get imagesField(): AbstractControl {
    return this.baseForm.get(ValidatorsField.IMAGES)!;
  }

  public get Limits(): IImageSize {
    return IMAGE_LIMITS;
  }

  handlePreview = async (file: NzUploadFile): Promise<void> => {
    if (!file.url && !file['preview']) {
      file['preview'] = await getBase64Async(file.originFileObj!);
    }
    this.previewImage = file.url || file['preview'];
    this.previewVisible = true;
  };

  public removeItem = (file: NzUploadFile): boolean => {
    this.uploadService.removeFiles(file);
    this.imagesField.patchValue(this.uploadService.FileList);
    this.uploadService.FileList.length <= 0 ||
    !this.uploadService.FileList.every((image) => image.error === undefined)
      ? this.isValidForm.set(false)
      : this.isValidForm.set(true);
    return false;
  };

  public showUploadList: ShowUploadListImage = {
    showPreviewIcon: true,
    showRemoveIcon: true,
    hidePreviewIconInNonImage: true,
  };

  public uploadRequest = (item: NzUploadXHRArgs) => {
    return this.imageService.uploadFileLocal(item, IMAGE_LIMITS).subscribe({
      next: (image) => {
        log('UPLOAD OK', image);
        of(image)
          .pipe(delay(200))
          .subscribe(() => {
            this.imagesField.patchValue(this.uploadService.FileList);
          });
      },
      error: (err) => {
        log('ERROR IMAGE', err);
      },
      complete: () => {
        log('IMAGE FILE LIST', this.uploadService.FileList);
      },
    });
  };

  public setMediaUploadHeaders = () => {
    return {
      'Content-Type': 'multipart/form-data',
      Accept: 'application/json',
    };
  };

  /**
   * Inizializza il form reattivo con i campi e le validazioni per l'evento.
   * Include validazioni per titolo, contenuto, data e immagini.
   * @returns void
   */
  private initForm() {
    this.baseForm = this.fb.group({
      id: ['', [Validators.nullValidator]],
      title: ['', [Validators.required, Validators.minLength(3)]],
      content: ['', [Validators.required, Validators.minLength(10)]],
      date: ['', [Validators.required]],
      isOnline: [true, [Validators.required]],
      images: ['', [Validators.required, ValidateImages(1, 1)]],
    });
  }

  /**
   * Imposta la modalità CRUD (create o update) in base ai dati di routing.
   * @returns void
   */
  override setCrudMode() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.setCreateMode();
        break;
      case crudActionType.update:
        this.setUpdateMode();
        break;
    }
  }

  /**
   * Configura il componente per la modalità di creazione di un nuovo evento.
   * @returns void
   */
  override setCreateMode() {
    this.baseForm.reset();
    this.initForm();
    this.eventsService.setEvent(undefined);
    this.saveButtonTitle.set('EVENTS.saveEvent');
    this.abortButtonTitle.set('abort');
    this.breadcrumbService.setIsLoading(false);
    this.loading.set(false);
  }

  /**
   * Configura il componente per la modalità di aggiornamento di un evento esistente.
   * Carica i dati dell'evento tramite HTTP, popola il form e configura il breadcrumb.
   * @returns void
   */
  override setUpdateMode() {
    this.saveButtonTitle.set('EVENTS.updateEvent');
    this.abortButtonTitle.set('EVENTS.deleteEvent');

    this.eventsService.readOne(this.eventId()).subscribe({
      next: (res) => {
        FormUtils.fillUpdateDataForm(this.baseForm, res.data!);
        this.initUploadService();
        const breadcrumbData = res.data?.title;
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.router.navigateByUrl('/events');
      },
    });
  }

  /**
   * Gestisce il click del pulsante di salvataggio.
   * Determina se eseguire creazione o aggiornamento in base alla modalità CRUD.
   * @returns void
   */
  onDataSaveClick() {
    if (this.crudMode() === crudActionType.create) {
      this.onDataSubmit();
    } else {
      this.onDataUpdate();
    }
  }
  /**
   * Hook di inizializzazione del componente.
   * Avvia l'osservazione dei cambiamenti del form.
   * @returns void
   */
  ngOnInit(): void {
    this.observeFormChanges();
  }

  /**
   * Osserva i cambiamenti del form e gestisce gli errori di upload delle immagini.
   * Imposta isValidForm in base alla validità del form e agli errori di upload.
   * @returns void
   */
  observeFormChanges() {
    this.imageService.uploadOnError$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.isValidForm.set(false);
      });

    this.baseForm.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.isValidForm.set(this.baseForm.valid);
      });
  }

  /**
   * Gestisce il click del pulsante di annullamento/eliminazione.
   * In modalità create: naviga alla lista eventi.
   * In modalità update: mostra modal di conferma per eliminazione.
   * @returns void
   */
  onAbortClick() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.router.navigateByUrl('/events');
        break;

      case crudActionType.update:
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.messageService.addLoadingMessage();
            this.eventsService.delete(this.eventId()).subscribe({
              next: () => {
                this.loading.set(false);
                this.router.navigateByUrl('/events');
                this.messageService.addSuccessMessage('EVENTS.deleteSuccess');
                log(`EVENT ID: ${this.eventId()} - Eliminato`);
              },
            });
          },
          title: 'EVENTS.confirmDeleteTitle',
          subtitle: 'EVENTS.confirmDeleteSubtitle',
        });
        break;
    }
  }

  /**
   * Gestisce la creazione di un nuovo evento.
   * Invia i dati del form al server tramite HTTP POST.
   * @returns void
   */
  override onDataSubmit() {
    this.loading.set(true);
    const event = this.baseForm.value;
    FormUtils.removeObjectNullProperties(event);
    this.messageService.addLoadingMessage('loading');
    this.eventsService.create(event).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('EVENTS.createSuccess');
        this.loading.set(false);
        this.router.navigate(['events', res.data!.id]);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  /**
   * Gestisce l'aggiornamento di un evento esistente.
   * Invia i dati modificati al server tramite HTTP PUT e aggiorna il breadcrumb.
   * @returns void
   */
  override onDataUpdate() {
    this.loading.set(true);
    const event = this.baseForm.value;
    this.messageService.addLoadingMessage('loading');

    this.eventsService.update(this.eventId(), event).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('EVENTS.updateSuccess');
        const breadcrumbData = res.data?.title;
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  /**
   * Cleanup del componente alla distruzione.
   * Resetta il breadcrumb service allo stato iniziale.
   * @returns void
   */
  ngOnDestroy(): void {
    this.breadcrumbService.reset();
  }
}
