<!-- MAIN CONTAINER -->
<div class="container">
  <!-- EVENTS FORM -->
  <form nz-form class="form" [formGroup]="baseForm" nzLayout="vertical">
    <!-- LOADING SPINNER -->
    <nz-spin [nzSpinning]="loading()">
      <!-- EVENTS INFO SECTION -->
      <div nz-row class="w-100" [nzGutter]="16">
        <div nz-col [nzSpan]="14">
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="24">
              <!-- EVENTS TITLE -->
              <app-input-generic
                [parentForm]="baseForm"
                [controlName]="'title'"
                [label]="'EVENTS.title' | translate"
                [placeholder]="'EVENTS.titlePlaceholder' | translate"
                [minLength]="3"
              ></app-input-generic>
            </div>

            <div nz-col [nzSpan]="24">
              <!-- EVENTS CONTENT -->
              <app-input-textarea
                [parentForm]="baseForm"
                [controlName]="'content'"
                [label]="'EVENTS.content' | translate"
                [placeholder]="'EVENTS.contentPlaceholder' | translate"
                [minLength]="10"
                [size]="{ minRows: 9, maxRows: 40 }"
              ></app-input-textarea>
            </div>
          </div>
        </div>
        <div nz-col [nzSpan]="10">
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="24">
              <!-- EVENTS STATUS -->
              <app-input-checkbox
                [parentForm]="baseForm"
                [controlName]="'isOnline'"
                [label]="'EVENTS.eventStatus' | translate"
                [optionList]="showEventStatus()"
                [name]="'EVENTS.showOnline' | translate"
              ></app-input-checkbox>
            </div>

            <div nz-col [nzSpan]="24">
              <!-- EVENTS DATE -->
              <app-input-single-datepicker
                [parentForm]="baseForm"
                [controlName]="'date'"
                [label]="'EVENTS.date'"
                [placeholder]="'EVENTS.datePlaceholder'"
                [labelPosition]="'top'"
                [nzFormat]="'yyyy-MM-dd'"
                [style]="{ maxWidth: '200px' }"
              ></app-input-single-datepicker>
            </div>

            <div nz-col [nzSpan]="24">
              <!-- EVENTS IMAGE UPLOAD -->
              <nz-form-item [formGroup]="baseForm">
                <nz-form-control>
                  <div class="clearfix">
                    <!-- IMAGE UPLOAD LABEL WITH INFO -->
                    <nz-form-label [nzRequired]="false" nzNoColon="true">
                      <i
                        nz-icon
                        class="info-icon"
                        nz-popover
                        [nzPopoverTitle]="'limits.limit' | translate"
                        [nzPopoverContent]="tplImageInfo"
                        nzType="info-circle"
                      ></i>
                      <strong class="info-image">{{
                        "image" | translate
                      }}</strong>
                      <!-- IMAGE LIMITS INFO TEMPLATE -->
                      <ng-template #tplImageInfo>
                        <div class="info-image">
                          <strong>{{ "limits.width" | translate }}: </strong>
                          <div class="min-max">
                            <span>min {{ Limits.width.min }}px</span>
                            <span>max {{ Limits.width.max }}px</span>
                          </div>
                          <strong>{{ "limits.heigth" | translate }}: </strong>
                          <div class="min-max">
                            <span>min {{ Limits.height.min }}px</span>
                            <span>max {{ Limits.height.max }}px</span>
                          </div>
                        </div>
                      </ng-template>
                    </nz-form-label>
                    <!-- IMAGE UPLOAD COMPONENT -->
                    <nz-upload
                      [nzAction]="'http://localhost:4201/api/fakeImage'"
                      [nzCustomRequest]="uploadRequest"
                      [(nzFileList)]="FileList"
                      [nzShowButton]="FileList.length < 1"
                      [nzFileType]="
                        'image/png,image/jpeg,image/gif,image/bmp,image/jpg,image/webp'
                      "
                      [nzHeaders]="setMediaUploadHeaders"
                      [nzPreview]="handlePreview"
                      [nzRemove]="removeItem"
                      [nzShowUploadList]="showUploadList"
                      [nzListType]="'picture-card'"
                      [nzAccept]="'image/*'"
                    >
                      <div>
                        <i nz-icon nzType="plus"></i>
                        <div style="margin-top: 8px">
                          {{ "upload" | translate }}
                        </div>
                      </div>
                    </nz-upload>
                    <!-- IMAGE PREVIEW MODAL -->
                    <nz-modal
                      [(nzVisible)]="previewVisible"
                      [nzContent]="modalContent"
                      [nzFooter]="null"
                      (nzOnCancel)="previewVisible = false"
                    >
                      <ng-template #modalContent>
                        <img
                          [src]="previewImage"
                          [style]="{ width: '100%' }"
                          alt="Immagine"
                        />
                      </ng-template>
                    </nz-modal>
                  </div>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </div>
      </div>
    </nz-spin>
  </form>
</div>

<!-- ACTION BUTTONS TEMPLATE -->
<ng-template #tplButton>
  <!-- SAVE/UPDATE BUTTON -->
  <button
    nz-button
    nzType="primary"
    (click)="onDataSaveClick()"
    [disabled]="!isValidForm() || loading()"
  >
    {{ saveButtonTitle() | translate }}
    @if (crudMode() === crudActionType.create) {
      <i nz-icon nzType="client-ui:plus"></i>
    } @else {
      <i nz-icon nzType="save"></i>
    }
  </button>
</ng-template>
