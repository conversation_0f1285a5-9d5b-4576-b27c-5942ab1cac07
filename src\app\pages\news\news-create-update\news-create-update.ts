import {
  Cdk<PERSON><PERSON>,
  CdkDrag<PERSON><PERSON>le,
  CdkDropList,
  moveItemInArray,
} from '@angular/cdk/drag-drop';
import {
  afterNextRender,
  Component,
  DestroyRef,
  inject,
  output,
  Signal,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NewsService } from '@core/services/http/news';
import { TagsService } from '@core/services/http/tags';
import { BreadcrumbService } from '@core/services/utils/breadcrumb';
import { HeaderService } from '@core/services/utils/header';
import { ImageService } from '@core/services/utils/image';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { UploadService, ValidateImages } from '@core/services/utils/upload';
import { FormUtils } from '@core/utils/form';
import { getBase64Async } from '@core/utils/image';
import { log } from '@core/utils/logger';
import { CreateUpdateItem } from '@models/entities/create-update-item';
import { crudActionType } from '@models/enums/crud-action-type';
import { currentSectionType } from '@models/enums/current-section';
import {
  IImageSize,
  ShowUploadListImage,
} from '@models/interfaces/file-upload';
import { IImage } from '@models/interfaces/image';
import {
  articleItemType,
  articleStatusType,
  INews,
  ITag,
} from '@models/interfaces/news';
import { TranslateModule } from '@ngx-translate/core';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { InputCheckboxComponent } from '@shared/inputs/input-checkbox/input-checkbox';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { InputSelectSearch } from '@shared/inputs/input-select-product/input-select-search';
import { InputSelectComponent } from '@shared/inputs/input-select/input-select';
import { InputSingleDatepickerComponent } from '@shared/inputs/input-single-datepicker/input-single-datepicker';
import { InputTextareaComponent } from '@shared/inputs/input-textarea/input-textarea';
import { InputUploadComponent } from '@shared/inputs/input-upload/input-upload.component';
import { TagNewsStatus } from '@shared/tags/tag-news-status/tag-news-status';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzDividerComponent } from 'ng-zorro-antd/divider';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzFormDirective } from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { NzUploadFile, NzUploadXHRArgs } from 'ng-zorro-antd/upload';
import { Editor, NgxEditorModule, Toolbar } from 'ngx-editor';
import { delay, of, switchMap, tap } from 'rxjs';

enum ValidatorsField {
  IMAGES = 'images',
}

const IMAGE_LIMITS: IImageSize = {
  width: {
    min: 200,
    max: 2000,
  },
  height: {
    min: 200,
    max: 2000,
  },
  size: {
    kilobytes: 16,
    megabytes: 16,
    gigabytes: 16,
  },
};

interface INewsForm {
  id: FormControl<string>;
  title: FormControl<string>;
  summary: FormControl<string>;
  images: FormControl<IImage[]>;
  url: FormControl<string>;
  status: FormControl<articleStatusType>;
  pinned: FormControl<boolean>;
  tags: FormControl<ITag[]>;
  items: FormArray<
    FormGroup<{
      type: FormControl<articleItemType>;
      order: FormControl<number>;
      content: FormControl<any>;
      readonly: FormControl<boolean>;
    }>
  >;
  orderDate: FormControl<Date>;
}

interface IItemUploadManager {
  fileList: NzUploadFile[];
  imageList: IImage[];
  uploadService: UploadService;
}

@Component({
  selector: 'app-news-create-update',
  imports: [
    ReactiveFormsModule,
    FormsModule,
    NzFormDirective,
    NzSpinComponent,
    NzRowDirective,
    NzColDirective,
    NzButtonComponent,
    NzIconDirective,
    TranslateModule,
    InputGenericComponent,
    InputTextareaComponent,
    InputSingleDatepickerComponent,
    InputCheckboxComponent,
    NzDropDownModule,
    NzDividerComponent,
    SimpleButtonComponent,
    InputSelectComponent,
    TagNewsStatus,
    InputUploadComponent,
    NzCardModule,
    CdkDropList,
    CdkDrag,
    CdkDragHandle,
    NgxEditorModule,
    InputSelectSearch,
  ],
  providers: [UploadService],
  templateUrl: './news-create-update.html',
  styleUrl: './news-create-update.less',
})
export class NewsCreateUpdate extends CreateUpdateItem {
  // SERVICES
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private uploadService = inject(UploadService);
  private imageService = inject(ImageService);
  private newsService = inject(NewsService);
  private router = inject(Router);
  private messageService = inject(MessageService);
  private modalService = inject(ModalService);
  private headerService = inject(HeaderService);
  private fb = inject(FormBuilder);
  private breadcrumbService = inject(BreadcrumbService);
  private tagsService = inject(TagsService);

  protected baseForm!: FormGroup<INewsForm>;
  // Convert to signals
  protected saveButtonTitle = signal<string>('NEWS.saveNews');
  protected abortButtonTitle = signal<string>('NEWS.deleteNews');
  public crudMode = signal<crudActionType>(crudActionType.create);
  protected crudActionType = crudActionType;
  protected isValidForm = signal<boolean>(false);
  protected newsId = signal<string>('');
  protected loading = signal<boolean>(true);
  protected news: Signal<INews | undefined> = this.newsService.news$;

  protected tagsList = signal<ITag[]>([]);
  protected isLoadingTagList = signal<boolean>(false);
  protected tagList = signal<{ label: string; value: string }[]>([]);

  // CONSTANT SIGNALS
  protected newsStatusStyle = signal<{ [key: string]: any } | undefined>(
    undefined,
  );

  // TEXT EDITOR
  editors = signal<
    {
      position: number;
      editor: Editor;
    }[]
  >([] as { position: number; editor: Editor }[]);
  toolbar = signal<Toolbar>([
    ['bold', 'italic'],
    ['underline', 'strike'],
    ['code', 'blockquote'],
    ['ordered_list', 'bullet_list'],
    [{ heading: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'] }],
    ['link'],
    ['text_color', 'background_color'],
    ['align_left', 'align_center', 'align_right', 'align_justify'],
  ]);

  // UPLOAD MANAGERS PER ITEM
  itemUploadManagers = signal<Map<number, IItemUploadManager>>(new Map());

  articleItemOptionList = signal<{ label: string; value: articleItemType }[]>([
    { label: 'NEWS.text', value: articleItemType.text },
    { label: 'NEWS.images', value: articleItemType.images },
    { label: 'NEWS.videos', value: articleItemType.videos },
  ]);

  // ENUMS
  articleStatusType = articleStatusType;
  articleItemType = articleItemType;

  /**
   * Crea o recupera l'upload manager per un item specifico
   */
  getItemUploadManager(index: number): IItemUploadManager {
    const managers = this.itemUploadManagers();
    if (!managers.has(index)) {
      const newManager: IItemUploadManager = {
        fileList: [],
        imageList: [],
        uploadService: new UploadService(),
      };
      managers.set(index, newManager);
      this.itemUploadManagers.set(new Map(managers));
    }
    return managers.get(index)!;
  }

  /**
   * Rimuove l'upload manager per un item specifico
   */
  removeItemUploadManager(index: number): void {
    const managers = this.itemUploadManagers();
    if (managers.has(index)) {
      managers.get(index)?.uploadService.clean();
      managers.delete(index);
      this.itemUploadManagers.set(new Map(managers));
    }
  }

  /**
   * Riordina gli upload managers quando gli items vengono riordinati
   */
  reorderItemUploadManagers(previousIndex: number, currentIndex: number): void {
    const managers = this.itemUploadManagers();
    const newManagers = new Map<number, IItemUploadManager>();

    // Ottieni il manager da spostare
    const managerToMove = managers.get(previousIndex);

    // Riordina tutti i manager
    managers.forEach((manager, index) => {
      if (index === previousIndex) {
        // Questo manager verrà spostato, non lo aggiungiamo qui
        return;
      }

      let newIndex = index;

      // Calcola la nuova posizione in base al movimento
      if (previousIndex < currentIndex) {
        // Movimento verso destra
        if (index > previousIndex && index <= currentIndex) {
          newIndex = index - 1;
        }
      } else {
        // Movimento verso sinistra
        if (index >= currentIndex && index < previousIndex) {
          newIndex = index + 1;
        }
      }

      newManagers.set(newIndex, manager);
    });

    // Aggiungi il manager spostato nella nuova posizione
    if (managerToMove) {
      newManagers.set(currentIndex, managerToMove);
    }

    this.itemUploadManagers.set(newManagers);
  }

  /**
   * Inizializza il servizio di upload con le immagini esistenti della news.
   * Popola FileList e ImageList con i dati dal form.
   * @returns void
   */
  private initUploadService(): void {
    this.uploadService.FileList = FormUtils.populateImages(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
    this.uploadService.ImageList = FormUtils.populateField(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
  }

  /**
   * Inizializza gli upload manager per gli items esistenti
   */
  private initItemUploadManagers(news: INews): void {
    news.items?.forEach((item, index) => {
      if (
        item.type === articleItemType.images ||
        item.type === articleItemType.videos
      ) {
        const manager = this.getItemUploadManager(index);

        // Se il content è un array di immagini, popolalo
        if (Array.isArray(item.content)) {
          const images = item.content as IImage[];
          manager.fileList = FormUtils.populateImages(
            { get: () => ({ value: images }) } as any,
            'value',
          );
          manager.imageList = images;
        }
      }

      // Sottoscrivi ai cambiamenti di tipo per ogni item esistente
      const itemControl = this.items.at(index);
      itemControl
        .get('type')
        ?.valueChanges.pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe((newType: articleItemType) => {
          this.onItemTypeChange(index, newType);
        });
    });
  }

  protected previewImage: string | undefined = '';
  protected previewVisible = false;

  // VIEWCHILD
  public tplButton = viewChild<TemplateRef<any>>('tplButton');

  // OUTPUT
  componentReady = output<TemplateRef<any>>();

  /**
   * Costruttore del componente news create/update.
   * Inizializza il form, gestisce i parametri di routing e configura l'header.
   * @returns void
   */
  constructor() {
    super();
    this.initForm();

    this.route.paramMap
      .pipe(
        takeUntilDestroyed(),
        switchMap((params) => {
          console.log(params);
          this.newsId.set(<string>params.get('id'));
          console.log(this.newsId());
          return this.route.data.pipe(
            tap((data) => {
              this.crudMode.set(<crudActionType>data['crudType']);
              this.setCrudMode();
            }),
          );
        }),
      )
      .subscribe();

    afterNextRender(() => {
      this.headerService.setCurrentSection(
        currentSectionType.newsDetail,
        this.tplButton(),
      );
    });
  }

  public get FileList(): NzUploadFile[] {
    return this.uploadService.FileList;
  }

  public set FileList(list: NzUploadFile[]) {
    this.uploadService.FileList = list;
  }

  public get ImageList(): IImage[] {
    return this.uploadService.ImageList;
  }

  public get imagesField(): AbstractControl {
    return this.baseForm.get(ValidatorsField.IMAGES)!;
  }

  public get Limits(): IImageSize {
    return IMAGE_LIMITS;
  }

  handlePreview = async (file: NzUploadFile): Promise<void> => {
    if (!file.url && !file['preview']) {
      file['preview'] = await getBase64Async(file.originFileObj!);
    }
    this.previewImage = file.url || file['preview'];
    this.previewVisible = true;
  };

  public removeItem = (file: NzUploadFile): boolean => {
    this.uploadService.removeFiles(file);
    this.imagesField.patchValue(this.uploadService.FileList);
    this.uploadService.FileList.length <= 0 ||
    !this.uploadService.FileList.every((image) => image.error === undefined)
      ? this.isValidForm.set(false)
      : this.isValidForm.set(true);
    return false;
  };

  public showUploadList: ShowUploadListImage = {
    showPreviewIcon: true,
    showRemoveIcon: true,
    hidePreviewIconInNonImage: true,
  };

  public uploadRequest = (item: NzUploadXHRArgs) => {
    return this.imageService.uploadFileLocal(item, IMAGE_LIMITS).subscribe({
      next: (image) => {
        log('UPLOAD OK', image);
        of(image)
          .pipe(delay(200))
          .subscribe(() => {
            this.imagesField.patchValue(this.uploadService.FileList);
          });
      },
      error: (err) => {
        log('ERROR IMAGE', err);
      },
      complete: () => {
        log('IMAGE FILE LIST', this.uploadService.FileList);
      },
    });
  };

  public setMediaUploadHeaders = () => {
    return {
      'Content-Type': 'multipart/form-data',
      Accept: 'application/json',
    };
  };

  /**
   * Inizializza il form reattivo con i campi e le validazioni per la news.
   * Include validazioni per titolo, contenuto, data e immagini.
   * @returns void
   */
  private initForm() {
    this.baseForm = this.fb.group({
      id: this.fb.control(''),
      title: this.fb.nonNullable.control('', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(120),
      ]),
      summary: this.fb.nonNullable.control('', [
        Validators.minLength(2),
        Validators.maxLength(300),
      ]),
      images: this.fb.nonNullable.control(
        [],
        [Validators.required, ValidateImages(1, 1)],
      ),
      url: this.fb.nonNullable.control(''),
      status: this.fb.nonNullable.control(articleStatusType.draft),
      pinned: this.fb.nonNullable.control(false),
      tags: this.fb.nonNullable.control([]),
      items: this.fb.array([this.createNewItem()]),
      orderDate: this.fb.nonNullable.control(new Date()),
    });

    this.baseForm.valueChanges.subscribe((res) =>
      console.log('FORM CHANGED', res),
    );
  }

  get items(): FormArray {
    return this.baseForm.get('items') as FormArray;
  }

  /**
   * Imposta la modalità CRUD (create o update) in base ai dati di routing.
   * @returns void
   */
  override setCrudMode() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.setCreateMode();
        break;
      case crudActionType.update:
        this.setUpdateMode();
        break;
    }
  }

  /**
   * Configura il componente per la modalità di creazione di una nuova news.
   * @returns void
   */
  override setCreateMode() {
    this.baseForm.reset();
    this.initForm();
    this.newsService.setNews(undefined);
    this.saveButtonTitle.set('NEWS.saveNews');
    this.abortButtonTitle.set('abort');
    this.breadcrumbService.setIsLoading(false);
    this.loading.set(false);
  }

  /**
   * Configura il componente per la modalità di aggiornamento di una news esistente.
   * Carica i dati della news tramite HTTP, popola il form e configura il breadcrumb.
   * @returns void
   */
  override setUpdateMode() {
    this.saveButtonTitle.set('NEWS.updateNews');
    this.abortButtonTitle.set('NEWS.deleteNews');

    this.newsService.readOne(this.newsId()).subscribe({
      next: (res) => {
        FormUtils.fillUpdateDataForm(this.baseForm, res.data!);
        this.initUploadService();
        this.initItemUploadManagers(res.data!);
        const breadcrumbData = res.data?.title;
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.router.navigateByUrl('/news');
      },
    });
  }

  /**
   * Gestisce il click del pulsante di salvataggio.
   * Determina se eseguire creazione o aggiornamento in base alla modalità CRUD.
   * @returns void
   */
  onDataSaveClick() {
    if (this.crudMode() === crudActionType.create) {
      this.onDataSubmit();
    } else {
      this.onDataUpdate();
    }
  }
  /**
   * Hook di inizializzazione del componente.
   * Avvia l'osservazione dei cambiamenti del form.
   * @returns void
   */
  ngOnInit(): void {
    this.observeFormChanges();
  }

  /**
   * Osserva i cambiamenti del form e gestisce gli errori di upload delle immagini.
   * Imposta isValidForm in base alla validità del form e agli errori di upload.
   * @returns void
   */
  observeFormChanges() {
    this.imageService.uploadOnError$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.isValidForm.set(false);
      });

    this.baseForm.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.isValidForm.set(this.baseForm.valid);
      });

    // Osserva i cambiamenti di tipo degli items
    this.items.controls.forEach((itemControl, index) => {
      itemControl
        .get('type')
        ?.valueChanges.pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe((newType: articleItemType) => {
          this.onItemTypeChange(index, newType);
        });
    });
  }

  /**
   * Gestisce il click del pulsante di annullamento/eliminazione.
   * In modalità create: naviga alla lista categorie.
   * In modalità update: mostra modal di conferma per eliminazione.
   * @returns void
   */
  onAbortClick() {
    // this.loading.set(true);
    switch (this.crudMode()) {
      case crudActionType.create:
        this.router.navigateByUrl('/categories');
        break;

      case crudActionType.update:
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.messageService.addLoadingMessage();
            this.newsService.delete(this.newsId()).subscribe({
              next: () => {
                this.loading.set(false);
                this.router.navigateByUrl('/categories');
                this.messageService.addSuccessMessage(
                  'CATEGORIES.deleteSuccess',
                );
                log(`CATEGORY ID: ${this.newsId()} - Eliminato`);
              },
            });
          },
          title: 'CATEGORIES.confirmDeleteTitle',
          subtitle: 'CATEGORIES.confirmDeleteSubtitle',
        });
        break;
    }
  }

  orderItems() {
    this.items.controls.forEach((item, index) => {
      item.get('order')?.patchValue(index);
    });
  }

  /**
   * Gestisce la creazione di una nuova news.
   * Invia i dati del form al server tramite HTTP POST.
   * @returns void
   */
  override onDataSubmit() {
    this.loading.set(true);
    this.orderItems();
    this.prepareItemsForSubmit();
    const news = this.baseForm.getRawValue();
    FormUtils.removeObjectNullProperties(news);
    this.messageService.addLoadingMessage('loading');
    this.newsService.create(news).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('NEWS.createSuccess');
        this.loading.set(false);
        this.router.navigate(['news', res.data!.id]);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  /**
   * Gestisce l'aggiornamento di una news esistente.
   * Invia i dati modificati al server tramite HTTP PUT e aggiorna il breadcrumb.
   * @returns void
   */
  override onDataUpdate() {
    this.loading.set(true);
    this.orderItems();
    this.prepareItemsForSubmit();
    const news = this.baseForm.getRawValue();
    this.messageService.addLoadingMessage('loading');

    this.newsService.update(this.newsId(), news).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('NEWS.updateSuccess');
        const breadcrumbData = res.data?.title;
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  /**
   * Prepara gli items per l'invio al server convertendo le FileList in IImage[]
   */
  private prepareItemsForSubmit(): void {
    this.items.controls.forEach((itemControl, index) => {
      const type = itemControl.get('type')?.value;

      if (type === articleItemType.images || type === articleItemType.videos) {
        const manager = this.getItemUploadManager(index);
        const images: any[] = manager.uploadService.FileList;
        itemControl.get('content')?.patchValue(images);
      }
    });
  }

  openNewTagModal() {}

  onStatusChange(value: articleStatusType) {
    this.baseForm.get('status')?.patchValue(value);
  }

  /**
   * Gestisce il cambio di tipo di un item
   */
  onItemTypeChange(index: number, newType: articleItemType) {
    const itemControl = this.items.at(index);
    const currentType = itemControl.get('type')?.value;

    if (currentType !== newType) {
      // Reset del content quando cambia il tipo
      if (newType === articleItemType.text) {
        itemControl.get('content')?.patchValue('');
      } else {
        itemControl.get('content')?.patchValue([]);
        // Assicurati che esista un upload manager per questo item
        this.getItemUploadManager(index);
      }

      // Aggiorna il tipo
      itemControl.get('type')?.patchValue(newType);
    }
  }

  addNewItem() {
    const newIndex = this.items?.length;
    const newItem = this.createNewItem(newIndex);
    this.items.push(newItem);

    // Sottoscrivi ai cambiamenti di tipo per il nuovo item
    newItem
      .get('type')
      ?.valueChanges.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((newType: articleItemType) => {
        this.onItemTypeChange(newIndex, newType);
      });
  }

  drop(event: any) {
    const previousIndex = event.previousIndex;
    const currentIndex = event.currentIndex;

    // Riordina i controlli del FormArray
    moveItemInArray(this.items.controls, previousIndex, currentIndex);

    // Riordina gli editor
    this.reorderEditorsAfterDrop(previousIndex, currentIndex);

    // Riordina gli upload manager
    this.reorderItemUploadManagers(previousIndex, currentIndex);

    // Aggiorna gli ordini
    this.orderItems();
  }

  /**
   * Riordina gli editor dopo il drag & drop
   */
  private reorderEditorsAfterDrop(
    previousIndex: number,
    currentIndex: number,
  ): void {
    this.editors.update((editors) => {
      // Crea una copia dell'array
      const newEditors = [...editors];

      // Trova l'editor da spostare
      const editorToMove = newEditors.find((e) => e.position === previousIndex);

      if (editorToMove) {
        // Rimuovi l'editor dalla posizione precedente
        const filteredEditors = newEditors.filter(
          (e) => e.position !== previousIndex,
        );

        // Aggiorna le posizioni di tutti gli editor
        const reorderedEditors = filteredEditors.map((editor) => {
          if (
            editor.position > previousIndex &&
            editor.position <= currentIndex
          ) {
            return { ...editor, position: editor.position - 1 };
          } else if (
            editor.position < previousIndex &&
            editor.position >= currentIndex
          ) {
            return { ...editor, position: editor.position + 1 };
          }
          return editor;
        });

        // Inserisci l'editor nella nuova posizione
        reorderedEditors.push({ ...editorToMove, position: currentIndex });

        // Ordina per posizione
        return reorderedEditors.sort((a, b) => a.position - b.position);
      }

      return newEditors;
    });
  }

  createNewItem(orderNum = 0) {
    // Crea editor per item di tipo text
    this.editors.update((editors) => [
      ...editors,
      { position: orderNum, editor: new Editor() },
    ]);

    // Crea upload manager per item di tipo images/videos
    this.getItemUploadManager(orderNum);

    return new FormGroup({
      type: this.fb.nonNullable.control(articleItemType.text),
      order: this.fb.nonNullable.control(orderNum),
      content: this.fb.nonNullable.control<any>(''),
      readonly: this.fb.nonNullable.control(false),
    });
  }

  deleteItem(index: number) {
    // Rimuovi l'item dal FormArray
    this.items.removeAt(index);

    // Rimuovi l'editor corrispondente
    this.editors.update((editors) => {
      const editorToDestroy = editors.find((e) => e.position === index);
      if (editorToDestroy) {
        editorToDestroy.editor.destroy();
      }
      // Filtra per posizione, non per indice dell'array
      return editors.filter((e) => e.position !== index);
    });

    // Rimuovi l'upload manager corrispondente
    this.removeItemUploadManager(index);

    // Riordina gli indici degli editor e upload manager rimanenti
    this.reorderEditorsAfterDeletion(index);
    this.reorderUploadManagersAfterDeletion(index);
  }

  /**
   * Riordina gli editor dopo l'eliminazione di un item
   */
  private reorderEditorsAfterDeletion(deletedIndex: number): void {
    this.editors.update((editors) =>
      editors.map((editor) => ({
        ...editor,
        position:
          editor.position > deletedIndex
            ? editor.position - 1
            : editor.position,
      })),
    );
  }

  /**
   * Riordina gli upload manager dopo l'eliminazione di un item
   */
  private reorderUploadManagersAfterDeletion(deletedIndex: number): void {
    const managers = this.itemUploadManagers();
    const newManagers = new Map<number, IItemUploadManager>();

    managers.forEach((manager, index) => {
      if (index > deletedIndex) {
        newManagers.set(index - 1, manager);
      } else if (index < deletedIndex) {
        newManagers.set(index, manager);
      }
    });

    this.itemUploadManagers.set(newManagers);
  }

  /**
   * Ottiene la FileList per un item specifico
   */
  getItemFileList(index: number): NzUploadFile[] {
    return this.getItemUploadManager(index).fileList;
  }

  /**
   * Ottiene la ImageList per un item specifico
   */
  getItemImageList(index: number): IImage[] {
    return this.getItemUploadManager(index).imageList;
  }

  /**
   * Gestisce l'upload per un item specifico
   */
  getItemUploadRequest(index: number) {
    return (item: NzUploadXHRArgs) => {
      const manager = this.getItemUploadManager(index);
      return this.imageService.uploadFileLocal(item, IMAGE_LIMITS).subscribe({
        next: (uploadResult) => {
          log('UPLOAD OK for item', index, uploadResult);
          of(uploadResult)
            .pipe(delay(200))
            .subscribe(() => {
              // Aggiorna la FileList del manager con il thumbUrl
              manager.uploadService.FileList =
                manager.uploadService.FileList.map((file) => ({
                  ...file,
                  thumbUrl: file.response?.base64,
                }));

              // Aggiorna il content del form item con le immagini
              const itemControl = this.items.at(index);
              const updatedImages: any[] = manager.uploadService.FileList;

              // Aggiorna anche la imageList del manager
              manager.imageList = updatedImages;

              // Aggiorna il FormControl
              itemControl.get('content')?.patchValue(updatedImages);

              log('Updated item content for index', index, updatedImages);
            });
        },
        error: (err) => {
          log('ERROR IMAGE for item', index, err);
        },
        complete: () => {
          log(
            'IMAGE FILE LIST for item',
            index,
            manager.uploadService.FileList,
          );
        },
      });
    };
  }

  /**
   * Gestisce la rimozione di un file per un item specifico
   */
  getItemRemoveHandler(index: number) {
    return (file: NzUploadFile): boolean => {
      const manager = this.getItemUploadManager(index);
      manager.uploadService.removeFiles(file);

      // Aggiorna il content del form item
      const itemControl = this.items.at(index);
      const updatedImages: any[] = manager.uploadService.FileList;

      // Aggiorna anche la imageList del manager
      manager.imageList = updatedImages;

      // Aggiorna il FormControl
      itemControl.get('content')?.patchValue(updatedImages);

      log('Updated item content after removal for index', index, updatedImages);

      return false;
    };
  }

  /**
   * Ottiene l'editor per una specifica posizione
   */
  getEditorForPosition(position: number): Editor | null {
    const editorData = this.editors().find((e) => e.position === position);
    return editorData ? editorData.editor : null;
  }

  searchTag(event: string) {
    this.isLoadingTagList.set(true);
    this.tagsService.readTagsByName(event).subscribe({
      next: (res) => {
        const tags = res.data!.map((product) => {
          return {
            label: product.name,
            value: product.id,
          };
        });
        this.tagList.set(tags);
        this.isLoadingTagList.set(false);
      },
    });
  }

  /**
   * Cleanup del componente alla distruzione.
   * Resetta il breadcrumb service allo stato iniziale.
   * Distrugge il text editor e pulisce gli upload manager.
   * @returns void
   */
  ngOnDestroy(): void {
    this.breadcrumbService.reset();
    this.editors().forEach((editor) => editor.editor.destroy());

    // Pulisci tutti gli upload manager
    this.itemUploadManagers().forEach((manager) => {
      manager.uploadService.clean();
    });
  }
}
