@import "mixin";

.themeMixin({
  :host {
    .icon-arrow {
      padding: 0 6px;
    }

    .drag-icon {
      font-size: 32px;
      font-weight: 700;
    }

    .editor {
      border: 2px solid @text-editor-border-color;
      border-radius: 4px;
    }
  }
});

.item-container {
  .cdk-drag-disabled {
    background: #ccc;
    cursor: not-allowed;
    user-select: none;
  }

  &:last-child {
    border: none;
  }
}



.item-list.cdk-drop-list-dragging .item-container:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}