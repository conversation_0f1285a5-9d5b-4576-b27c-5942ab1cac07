import { Component, inject } from '@angular/core';
import { HeaderService } from '@core/services/utils/header';
import { currentSectionType } from '@models/enums/current-section';

@Component({
  selector: 'app-news-list',
  imports: [],
  templateUrl: './news-list.html',
  styleUrl: './news-list.less',
})
export class NewsList {
  private headerService = inject(HeaderService);

  constructor() {
    this.headerService.setCurrentSection(currentSectionType.newsList);
  }
}
